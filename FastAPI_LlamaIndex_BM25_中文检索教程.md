# FastAPI + LlamaIndex + jieba + BM25 中文文档检索项目教程

## 项目概述

本教程将指导你创建一个基于 FastAPI、LlamaIndex、jieba 分词和 BM25 算法的中文文档检索系统，包含完整的前端展示页面。

## 技术栈

- **后端**: FastAPI + LlamaIndex + jieba + rank-bm25
- **数据库**: Qdrant (稀疏向量存储)
- **前端**: HTML + Bootstrap + JavaScript
- **分词**: jieba (中文分词)
- **检索算法**: BM25

## 项目结构

```
project/
├── app/
│   ├── main.py              # FastAPI 主应用
│   ├── models.py            # 数据模型
│   ├── services/
│   │   ├── bm25_service.py  # BM25 检索服务
│   │   └── document_service.py # 文档处理服务
│   └── static/
│       ├── index.html       # 前端页面
│       ├── css/
│       │   └── style.css
│       └── js/
│           └── app.js
├── data/
│   └── documents/           # 存放待检索的文档
├── requirements.txt
└── README.md
```

## 第一步：环境准备

### 1.1 安装依赖

```bash
pip install fastapi uvicorn
pip install llama-index-core llama-index-retrievers-bm25
pip install qdrant-client>=1.7
pip install jieba rank-bm25
pip install python-multipart  # 文件上传支持
```

### 1.2 启动 Qdrant

```bash
# 使用 Docker 启动 Qdrant
docker run -p 6333:6333 qdrant/qdrant
```

## 第二步：后端实现

### 2.1 数据模型 (models.py)

```python
from pydantic import BaseModel
from typing import List, Optional

class DocumentUpload(BaseModel):
    filename: str
    content: str

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 5

class SearchResult(BaseModel):
    text: str
    score: float
    source: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
```

### 2.2 BM25 检索服务 (services/bm25_service.py)

```python
import jieba
from rank_bm25 import BM25Okapi
from qdrant_client import QdrantClient, models
from typing import List, Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class BM25Service:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.client = QdrantClient(url=qdrant_url)
        self.collection_name = "chinese_documents_bm25"
        self.bm25 = None
        self.vocab = {}
        self.ensure_collection()

    def ensure_collection(self):
        """确保 Qdrant collection 存在"""
        collections = {c.name for c in self.client.get_collections().collections}
        if self.collection_name not in collections:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config={},  # 无密集向量
                sparse_vectors_config={
                    "bm25": models.SparseVectorParams(
                        index=models.SparseIndexParams()
                    )
                }
            )
            logger.info(f"Created collection: {self.collection_name}")

    def tokenize_text(self, text: str) -> List[str]:
        """中文分词"""
        return jieba.lcut(text)

    def build_bm25_index(self, documents: List[str]) -> None:
        """构建 BM25 索引"""
        token_lists = [self.tokenize_text(doc) for doc in documents]
        self.bm25 = BM25Okapi(token_lists)
        self.vocab = {token: idx for idx, token in enumerate(self.bm25._doc_freqs)}
        logger.info(f"Built BM25 index with {len(self.vocab)} tokens")

    def to_sparse_vector(self, tokens: List[str]) -> Tuple[List[int], List[float]]:
        """将分词结果转换为稀疏向量"""
        if not self.bm25 or not self.vocab:
            return [], []

        tf = {}
        for token in tokens:
            tf[token] = tf.get(token, 0) + 1

        indices, values = [], []
        for token, count in tf.items():
            if token not in self.vocab:
                continue

            idf = self.bm25.idf.get(token, 0)
            weight = idf * count * (self.bm25.k1 + 1) / (count + self.bm25.k1)

            indices.append(self.vocab[token])
            values.append(float(weight))

        return indices, values

    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到索引"""
        try:
            # 重新构建 BM25 索引
            texts = [doc["content"] for doc in documents]
            self.build_bm25_index(texts)

            # 生成稀疏向量并存储到 Qdrant
            batch = []
            for idx, doc in enumerate(documents):
                tokens = self.tokenize_text(doc["content"])
                indices, values = self.to_sparse_vector(tokens)

                point = models.PointStruct(
                    id=idx,
                    vector={"bm25": models.SparseVector(indices=indices, values=values)},
                    payload={
                        "text": doc["content"],
                        "source": doc.get("filename", f"doc_{idx}"),
                        "tokens": len(tokens)
                    }
                )
                batch.append(point)

            # 批量插入
            if batch:
                self.client.upsert(self.collection_name, batch)
                logger.info(f"Added {len(batch)} documents to collection")

            return True
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return False

    def search(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            if not self.bm25 or not self.vocab:
                return []

            # 对查询进行分词和向量化
            query_tokens = self.tokenize_text(query)
            query_indices, query_values = self.to_sparse_vector(query_tokens)

            if not query_indices:
                return []

            # 在 Qdrant 中搜索
            search_result = self.client.search(
                collection_name=self.collection_name,
                sparse_vector=models.SparseVector(
                    indices=query_indices,
                    values=query_values
                ),
                limit=limit,
                with_payload=True
            )

            results = []
            for hit in search_result:
                results.append({
                    "text": hit.payload["text"],
                    "score": hit.score,
                    "source": hit.payload["source"]
                })

            return results
        except Exception as e:
            logger.error(f"Error searching: {e}")
            return []
```

### 2.3 文档处理服务 (services/document_service.py)

```python
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import SentenceSplitter
from typing import List, Dict, Any
import os
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self):
        self.splitter = SentenceSplitter(
            chunk_size=700,
            chunk_overlap=100,
            lang="zh"  # 中文句子分割
        )

    def process_uploaded_file(self, content: str, filename: str) -> List[Dict[str, Any]]:
        """处理上传的文件内容"""
        try:
            # 将内容分割成块
            chunks = self.splitter.split_text(content)

            documents = []
            for i, chunk in enumerate(chunks):
                documents.append({
                    "content": chunk,
                    "filename": f"{filename}_chunk_{i}",
                    "original_file": filename
                })

            logger.info(f"Processed {filename} into {len(documents)} chunks")
            return documents
        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            return []

    def load_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """从目录加载文档"""
        try:
            if not os.path.exists(directory_path):
                return []

            reader = SimpleDirectoryReader(directory_path)
            docs = reader.load_data()

            all_documents = []
            for doc in docs:
                nodes = self.splitter.get_nodes_from_documents([doc])
                for i, node in enumerate(nodes):
                    all_documents.append({
                        "content": node.text,
                        "filename": f"{doc.metadata.get('file_name', 'unknown')}_chunk_{i}",
                        "original_file": doc.metadata.get('file_name', 'unknown')
                    })

            logger.info(f"Loaded {len(all_documents)} document chunks from {directory_path}")
            return all_documents
        except Exception as e:
            logger.error(f"Error loading directory {directory_path}: {e}")
            return []
```

## 第三步：FastAPI 主应用 (main.py)

```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import logging
from typing import List

from models import SearchRequest, SearchResponse, SearchResult
from services.bm25_service import BM25Service
from services.document_service import DocumentService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="中文文档 BM25 检索系统", version="1.0.0")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化服务
bm25_service = BM25Service()
document_service = DocumentService()

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    try:
        all_documents = []

        for file in files:
            if not file.filename.endswith(('.txt', '.md')):
                raise HTTPException(status_code=400, detail="只支持 .txt 和 .md 文件")

            content = await file.read()
            content = content.decode('utf-8')

            documents = document_service.process_uploaded_file(content, file.filename)
            all_documents.extend(documents)

        # 添加到 BM25 索引
        success = bm25_service.add_documents(all_documents)

        if success:
            return {
                "message": f"成功上传并处理了 {len(files)} 个文件，生成 {len(all_documents)} 个文档块",
                "document_count": len(all_documents)
            }
        else:
            raise HTTPException(status_code=500, detail="文档处理失败")

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """搜索文档"""
    try:
        results = bm25_service.search(request.query, request.limit)

        search_results = [
            SearchResult(
                text=result["text"][:200] + "..." if len(result["text"]) > 200 else result["text"],
                score=result["score"],
                source=result["source"]
            )
            for result in results
        ]

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query
        )
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "BM25 中文检索系统"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 第四步：前端页面实现

### 4.1 HTML 页面 (static/index.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中文文档 BM25 检索系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3 sidebar">
          <div class="sidebar-header">
            <h4><i class="fas fa-search"></i> BM25 检索系统</h4>
          </div>

          <!-- 文件上传区域 -->
          <div class="upload-section">
            <h5><i class="fas fa-upload"></i> 文档上传</h5>
            <div class="mb-3">
              <input
                type="file"
                class="form-control"
                id="fileInput"
                multiple
                accept=".txt,.md"
              />
            </div>
            <button type="button" class="btn btn-primary w-100" id="uploadBtn">
              <i class="fas fa-cloud-upload-alt"></i> 上传文档
            </button>
            <div id="uploadStatus" class="mt-2"></div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-section">
            <h5><i class="fas fa-search"></i> 文档搜索</h5>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="searchInput"
                placeholder="输入搜索关键词..."
              />
            </div>
            <div class="mb-3">
              <label for="limitSelect" class="form-label">结果数量:</label>
              <select class="form-select" id="limitSelect">
                <option value="5">5 条</option>
                <option value="10">10 条</option>
                <option value="20">20 条</option>
              </select>
            </div>
            <button type="button" class="btn btn-success w-100" id="searchBtn">
              <i class="fas fa-search"></i> 搜索
            </button>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="col-md-9 main-content">
          <div class="content-header">
            <h2>检索结果</h2>
            <div id="searchInfo" class="text-muted"></div>
          </div>

          <!-- 加载指示器 -->
          <div id="loadingIndicator" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理...</p>
          </div>

          <!-- 搜索结果 -->
          <div id="searchResults"></div>

          <!-- 欢迎信息 -->
          <div id="welcomeMessage" class="welcome-message">
            <div class="text-center">
              <i class="fas fa-file-text fa-5x text-muted mb-4"></i>
              <h3>欢迎使用 BM25 中文文档检索系统</h3>
              <p class="lead">请先上传文档，然后进行搜索</p>
              <div class="features">
                <div class="row">
                  <div class="col-md-4">
                    <i class="fas fa-language fa-2x text-primary"></i>
                    <h5>中文分词</h5>
                    <p>使用 jieba 进行精确的中文分词</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-bolt fa-2x text-warning"></i>
                    <h5>BM25 算法</h5>
                    <p>基于 BM25 的高效文档检索</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-database fa-2x text-success"></i>
                    <h5>向量存储</h5>
                    <p>使用 Qdrant 稀疏向量存储</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
  </body>
</html>
```

### 4.2 CSS 样式 (static/css/style.css)

```css
body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.sidebar {
  background-color: #343a40;
  color: white;
  min-height: 100vh;
  padding: 20px;
}

.sidebar-header {
  border-bottom: 1px solid #495057;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.sidebar-header h4 {
  margin: 0;
  color: #fff;
}

.upload-section,
.search-section {
  background-color: #495057;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.upload-section h5,
.search-section h5 {
  color: #fff;
  margin-bottom: 15px;
  border-bottom: 1px solid #6c757d;
  padding-bottom: 8px;
}

.main-content {
  padding: 20px;
}

.content-header {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.search-result-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.search-result-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-score {
  background-color: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
}

.result-source {
  color: #6c757d;
  font-size: 0.9em;
  margin-bottom: 8px;
}

.result-text {
  line-height: 1.6;
  color: #333;
}

.welcome-message {
  text-align: center;
  padding: 60px 20px;
}

.features {
  margin-top: 40px;
}

.features .col-md-4 {
  padding: 20px;
}

.features h5 {
  margin: 15px 0 10px 0;
  color: #333;
}

.features p {
  color: #6c757d;
  font-size: 0.9em;
}

.alert-custom {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#uploadStatus .alert {
  margin-top: 10px;
  margin-bottom: 0;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.form-control,
.form-select {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    min-height: auto;
    margin-bottom: 20px;
  }

  .main-content {
    padding: 15px;
  }

  .welcome-message {
    padding: 30px 15px;
  }

  .features .col-md-4 {
    margin-bottom: 20px;
  }
}

/* 加载动画 */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* 搜索结果高亮 */
.highlight {
  background-color: #fff3cd;
  padding: 2px 4px;
  border-radius: 3px;
}
```

## 第五步：优化后的核心代码（基于官方文档最佳实践）

### 5.1 混合检索服务 (services/hybrid_search_service.py)

```python
import jieba
import uuid
import math
from typing import List, Dict, Any, Tuple
from sklearn.feature_extraction.text import TfidfVectorizer
from sentence_transformers import SentenceTransformer
from qdrant_client import QdrantClient, models
from llama_index.core import VectorStoreIndex, StorageContext, Document, Settings
from llama_index.core.node_parser import SentenceSplitter
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.retrievers.bm25 import BM25Retriever
import logging

logger = logging.getLogger(__name__)

class HybridSearchService:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.client = QdrantClient(url=qdrant_url)
        self.collection_name = "chinese_hybrid_docs"
        self.tfidf = None
        self.embed_model = None
        self.vector_store = None
        self.index = None
        self.bm25_retriever = None
        self.setup_models()
        self.setup_vector_store()

    def setup_models(self):
        """设置嵌入模型和TF-IDF"""
        # 设置中文嵌入模型
        try:
            self.embed_model = SentenceTransformer("BAAI/bge-base-zh-v1.5")
        except:
            # 如果没有安装sentence-transformers，使用默认模型
            logger.warning("未找到sentence-transformers，使用默认嵌入模型")
            from llama_index.embeddings.openai import OpenAIEmbedding
            self.embed_model = OpenAIEmbedding()

        Settings.embed_model = self.embed_model

        # 初始化TF-IDF向量化器
        self.tfidf = TfidfVectorizer(
            tokenizer=self.jieba_tokenizer,
            analyzer="word",
            token_pattern=None,
            lowercase=False
        )

    def jieba_tokenizer(self, text: str) -> List[str]:
        """jieba分词器"""
        return [tok.strip() for tok in jieba.lcut(text) if tok.strip()]

    def _csr_to_indices_values(self, mat_row) -> Tuple[List[int], List[float]]:
        """将CSR矩阵行转换为indices和values"""
        coo = mat_row.tocoo()
        return coo.col.tolist(), coo.data.tolist()

    def sparse_doc_vectors(self, texts: List[str]) -> Tuple[List[List[int]], List[List[float]]]:
        """生成文档的稀疏向量"""
        if self.tfidf is None:
            return [], []

        mat = self.tfidf.transform(texts)
        indices_list, values_list = [], []

        for i in range(mat.shape[0]):
            indices, values = self._csr_to_indices_values(mat.getrow(i))
            indices_list.append(indices)
            values_list.append(values)

        return indices_list, values_list

    def sparse_query_vectors(self, texts: List[str]) -> Tuple[List[List[int]], List[List[float]]]:
        """生成查询的稀疏向量"""
        return self.sparse_doc_vectors(texts)

    def setup_vector_store(self):
        """设置向量存储"""
        # 确保集合存在
        try:
            collections = {c.name for c in self.client.get_collections().collections}
            if self.collection_name not in collections:
                # 创建支持混合检索的集合
                self.client.create_collection(
                    collection_name=self.collection_name,
                    vectors_config={
                        "dense": models.VectorParams(
                            size=768,  # BGE模型的维度，如果使用其他模型需要调整
                            distance=models.Distance.COSINE
                        )
                    },
                    sparse_vectors_config={
                        "sparse": models.SparseVectorParams(
                            index=models.SparseIndexParams(on_disk=False)
                        )
                    }
                )
                logger.info(f"创建集合: {self.collection_name}")
        except Exception as e:
            logger.error(f"设置集合失败: {e}")

        # 创建向量存储
        self.vector_store = QdrantVectorStore(
            collection_name=self.collection_name,
            client=self.client,
            enable_hybrid=True,
            sparse_doc_fn=self.sparse_doc_vectors,
            sparse_query_fn=self.sparse_query_vectors
        )

    def add_documents_from_files(self, file_contents: List[Dict[str, str]]) -> bool:
        """从文件内容添加文档"""
        try:
            # 创建文档对象
            documents = []
            all_texts = []

            for file_data in file_contents:
                content = file_data["content"]
                doc = Document(
                    text=content,
                    metadata={"filename": file_data["filename"]},
                    doc_id=str(uuid.uuid4())
                )
                documents.append(doc)
                all_texts.append(content)

            # 使用文档分割器
            splitter = SentenceSplitter(
                chunk_size=512,
                chunk_overlap=50,
                lang="zh"
            )

            # 获取分割后的节点
            nodes = splitter.get_nodes_from_documents(documents)
            node_texts = [node.text for node in nodes]

            # 训练TF-IDF模型
            if node_texts:
                self.tfidf.fit(node_texts)
                vocab_size = len(self.tfidf.vocabulary_)
                logger.info(f"TF-IDF词汇表大小: {vocab_size}")

            # 创建存储上下文
            storage_context = StorageContext.from_defaults(
                vector_store=self.vector_store
            )

            # 创建索引（自动处理密向量嵌入和稀疏向量）
            self.index = VectorStoreIndex.from_documents(
                documents,
                storage_context=storage_context,
                transformations=[splitter]
            )

            # 创建BM25检索器作为备用
            self.bm25_retriever = BM25Retriever.from_defaults(
                nodes=nodes,
                similarity_top_k=10
            )

            logger.info(f"成功处理 {len(documents)} 个文档，生成 {len(nodes)} 个节点")
            return True

        except Exception as e:
            logger.error(f"添加文档时出错: {e}")
            return False

    def search(self, query: str, limit: int = 5, mode: str = "hybrid", alpha: float = 0.5) -> List[Dict[str, Any]]:
        """搜索文档

        Args:
            query: 查询文本
            limit: 返回结果数量
            mode: 搜索模式 ("hybrid", "sparse", "dense", "bm25")
            alpha: 混合搜索中稀疏向量的权重 (0.0-1.0)
        """
        try:
            if not self.index:
                return []

            if mode == "bm25" and self.bm25_retriever:
                # 纯BM25搜索（备用方案）
                query_tokens = " ".join(jieba.cut(query))
                nodes = self.bm25_retriever.retrieve(query_tokens)

                results = []
                for node in nodes[:limit]:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "bm25"
                    })
                return results

            elif mode == "hybrid":
                # 混合搜索（稀疏向量 + 密向量）
                query_engine = self.index.as_query_engine(
                    similarity_top_k=limit,
                    sparse_top_k=limit * 2,  # 稀疏向量多取一些候选
                    alpha=alpha,  # 稀疏向量权重
                    vector_store_query_mode="hybrid"
                )

                # 使用检索器获取节点
                retriever = self.index.as_retriever(
                    similarity_top_k=limit,
                    sparse_top_k=limit * 2,
                    vector_store_query_mode="hybrid"
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes[:limit]:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "hybrid"
                    })
                return results

            elif mode == "sparse":
                # 纯稀疏向量搜索
                retriever = self.index.as_retriever(
                    similarity_top_k=0,  # 不使用密向量
                    sparse_top_k=limit,
                    vector_store_query_mode="hybrid"
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "sparse"
                    })
                return results

            else:  # mode == "dense"
                # 纯密向量搜索
                retriever = self.index.as_retriever(
                    similarity_top_k=limit,
                    vector_store_query_mode="default"
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown"),
                        "mode": "dense"
                    })
                return results

        except Exception as e:
            logger.error(f"搜索时出错: {e}")
            return []

    def get_stats(self) -> Dict[str, Any]:
        """获取服务统计信息"""
        try:
            collection_info = self.client.get_collection(self.collection_name)
            vocab_size = len(self.tfidf.vocabulary_) if self.tfidf else 0

            return {
                "collection_name": self.collection_name,
                "points_count": collection_info.points_count,
                "vectors_count": collection_info.vectors_count,
                "vocab_size": vocab_size,
                "status": "ready" if self.index else "not_ready"
            }
        except Exception as e:
            logger.error(f"获取统计信息时出错: {e}")
            return {"status": "error", "error": str(e)}
```

### 5.2 混合检索 FastAPI 主应用 (hybrid_main.py)

```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="中文文档混合检索系统", version="2.0.0")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 导入混合检索服务
from services.hybrid_search_service import HybridSearchService

# 初始化服务
search_service = HybridSearchService()

# 数据模型
class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 5
    mode: Optional[str] = "hybrid"  # "hybrid", "sparse", "dense", "bm25"
    alpha: Optional[float] = 0.5  # 混合搜索中稀疏向量权重

class SearchResult(BaseModel):
    text: str
    score: float
    source: str
    mode: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
    mode: str
    alpha: Optional[float] = None

class StatsResponse(BaseModel):
    collection_name: str
    points_count: int
    vectors_count: Optional[int] = None
    vocab_size: int
    status: str

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    try:
        file_contents = []

        for file in files:
            if not file.filename.endswith(('.txt', '.md', '.pdf')):
                raise HTTPException(status_code=400, detail="支持 .txt、.md 和 .pdf 文件")

            content = await file.read()

            # 处理不同文件类型
            if file.filename.endswith('.pdf'):
                # 这里可以添加PDF解析逻辑
                # 暂时跳过PDF文件
                logger.warning(f"暂不支持PDF文件: {file.filename}")
                continue
            else:
                content = content.decode('utf-8')

            file_contents.append({
                "content": content,
                "filename": file.filename
            })

        if not file_contents:
            raise HTTPException(status_code=400, detail="没有有效的文件内容")

        # 添加到混合索引
        success = search_service.add_documents_from_files(file_contents)

        if success:
            return {
                "message": f"成功上传并处理了 {len(file_contents)} 个文件",
                "file_count": len(file_contents),
                "total_uploaded": len(files)
            }
        else:
            raise HTTPException(status_code=500, detail="文档处理失败")

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """搜索文档"""
    try:
        results = search_service.search(
            query=request.query,
            limit=request.limit,
            mode=request.mode,
            alpha=request.alpha
        )

        search_results = [
            SearchResult(
                text=result["text"][:300] + "..." if len(result["text"]) > 300 else result["text"],
                score=result["score"],
                source=result["source"],
                mode=result.get("mode", request.mode)
            )
            for result in results
        ]

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            mode=request.mode,
            alpha=request.alpha if request.mode == "hybrid" else None
        )
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/stats", response_model=StatsResponse)
async def get_stats():
    """获取系统统计信息"""
    try:
        stats = search_service.get_stats()
        return StatsResponse(**stats)
    except Exception as e:
        logger.error(f"Stats error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {
        "status": "healthy",
        "service": "中文文档混合检索系统",
        "version": "2.0.0"
    }

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 第六步：项目配置和启动

### 6.1 更新的 requirements.txt

```txt
# 核心框架
fastapi==0.104.1
uvicorn[standard]==0.24.0

# LlamaIndex 相关
llama-index-core==0.10.57
llama-index-vector-stores-qdrant==0.2.8
llama-index-retrievers-bm25==0.1.3
llama-index-embeddings-openai==0.1.9

# 向量数据库
qdrant-client>=1.7.0

# 中文处理
jieba==0.42.1

# 机器学习
scikit-learn==1.3.2
sentence-transformers==2.2.2

# 文件处理
python-multipart==0.0.6

# 可选依赖（用于更好的中文嵌入）
# torch>=1.9.0
# transformers>=4.21.0
```

### 6.2 Docker Compose 配置 (docker-compose.yml)

```yaml
version: "3.8"

services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

volumes:
  qdrant_data:
```

### 6.3 启动脚本 (start.py)

```python
import subprocess
import sys
import os
import time

def check_qdrant():
    """检查 Qdrant 是否运行"""
    try:
        import requests
        response = requests.get("http://localhost:6333/health")
        return response.status_code == 200
    except:
        return False

def start_qdrant():
    """启动 Qdrant"""
    print("启动 Qdrant...")
    subprocess.run(["docker-compose", "up", "-d", "qdrant"])

    # 等待 Qdrant 启动
    for i in range(30):
        if check_qdrant():
            print("Qdrant 启动成功!")
            return True
        time.sleep(1)
        print(f"等待 Qdrant 启动... ({i+1}/30)")

    print("Qdrant 启动失败!")
    return False

def main():
    # 确保必要的目录存在
    os.makedirs("static/css", exist_ok=True)
    os.makedirs("static/js", exist_ok=True)
    os.makedirs("services", exist_ok=True)

    # 启动 Qdrant
    if not check_qdrant():
        if not start_qdrant():
            sys.exit(1)
    else:
        print("Qdrant 已经在运行")

    print("启动 FastAPI 应用...")
    print("访问地址: http://localhost:8000")

    # 启动 FastAPI
    subprocess.run([
        sys.executable, "-m", "uvicorn",
        "simple_main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ])

if __name__ == "__main__":
    main()
```

### 6.4 前端页面增强 (static/index_enhanced.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中文文档 BM25 检索系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3 sidebar">
          <div class="sidebar-header">
            <h4><i class="fas fa-search"></i> BM25 检索系统</h4>
          </div>

          <!-- 文件上传区域 -->
          <div class="upload-section">
            <h5><i class="fas fa-upload"></i> 文档上传</h5>
            <div class="mb-3">
              <input
                type="file"
                class="form-control"
                id="fileInput"
                multiple
                accept=".txt,.md"
              />
            </div>
            <button type="button" class="btn btn-primary w-100" id="uploadBtn">
              <i class="fas fa-cloud-upload-alt"></i> 上传文档
            </button>
            <div id="uploadStatus" class="mt-2"></div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-section">
            <h5><i class="fas fa-search"></i> 文档搜索</h5>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="searchInput"
                placeholder="输入搜索关键词..."
              />
            </div>
            <div class="mb-3">
              <label for="limitSelect" class="form-label">结果数量:</label>
              <select class="form-select" id="limitSelect">
                <option value="5">5 条</option>
                <option value="10">10 条</option>
                <option value="20">20 条</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="modeSelect" class="form-label">搜索模式:</label>
              <select class="form-select" id="modeSelect">
                <option value="hybrid">混合搜索 (推荐)</option>
                <option value="sparse">稀疏向量 (关键词)</option>
                <option value="dense">密向量 (语义)</option>
                <option value="bm25">BM25 (备用)</option>
              </select>
            </div>
            <div class="mb-3" id="alphaControl" style="display: none;">
              <label for="alphaRange" class="form-label"
                >稀疏向量权重: <span id="alphaValue">0.5</span></label
              >
              <input
                type="range"
                class="form-range"
                id="alphaRange"
                min="0"
                max="1"
                step="0.1"
                value="0.5"
              />
              <small class="text-muted">0.0=纯密向量, 1.0=纯稀疏向量</small>
            </div>
            <button type="button" class="btn btn-success w-100" id="searchBtn">
              <i class="fas fa-search"></i> 搜索
            </button>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="col-md-9 main-content">
          <div class="content-header">
            <h2>检索结果</h2>
            <div id="searchInfo" class="text-muted"></div>
          </div>

          <!-- 加载指示器 -->
          <div id="loadingIndicator" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理...</p>
          </div>

          <!-- 搜索结果 -->
          <div id="searchResults"></div>

          <!-- 欢迎信息 -->
          <div id="welcomeMessage" class="welcome-message">
            <div class="text-center">
              <i class="fas fa-file-text fa-5x text-muted mb-4"></i>
              <h3>欢迎使用 BM25 中文文档检索系统</h3>
              <p class="lead">请先上传文档，然后进行搜索</p>
              <div class="features">
                <div class="row">
                  <div class="col-md-4">
                    <i class="fas fa-language fa-2x text-primary"></i>
                    <h5>中文分词</h5>
                    <p>使用 jieba 进行精确的中文分词</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-bolt fa-2x text-warning"></i>
                    <h5>混合搜索</h5>
                    <p>BM25 + 向量搜索的最佳组合</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-database fa-2x text-success"></i>
                    <h5>Qdrant 存储</h5>
                    <p>高性能向量数据库</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/enhanced_app.js"></script>
  </body>
</html>
```

### 6.5 增强版 JavaScript (static/js/enhanced_app.js)

```javascript
class HybridSearchApp {
  constructor() {
    this.initializeElements();
    this.bindEvents();
    this.showWelcomeMessage();
    this.loadStats();
  }

  initializeElements() {
    this.fileInput = document.getElementById("fileInput");
    this.uploadBtn = document.getElementById("uploadBtn");
    this.uploadStatus = document.getElementById("uploadStatus");
    this.searchInput = document.getElementById("searchInput");
    this.searchBtn = document.getElementById("searchBtn");
    this.limitSelect = document.getElementById("limitSelect");
    this.modeSelect = document.getElementById("modeSelect");
    this.alphaControl = document.getElementById("alphaControl");
    this.alphaRange = document.getElementById("alphaRange");
    this.alphaValue = document.getElementById("alphaValue");
    this.searchResults = document.getElementById("searchResults");
    this.searchInfo = document.getElementById("searchInfo");
    this.loadingIndicator = document.getElementById("loadingIndicator");
    this.welcomeMessage = document.getElementById("welcomeMessage");
  }

  bindEvents() {
    this.uploadBtn.addEventListener("click", () => this.uploadFiles());
    this.searchBtn.addEventListener("click", () => this.searchDocuments());
    this.searchInput.addEventListener("keypress", (e) => {
      if (e.key === "Enter") {
        this.searchDocuments();
      }
    });

    // 搜索模式变化时显示/隐藏alpha控制
    this.modeSelect.addEventListener("change", () => {
      if (this.modeSelect.value === "hybrid") {
        this.alphaControl.style.display = "block";
      } else {
        this.alphaControl.style.display = "none";
      }
    });

    // Alpha值变化时更新显示
    this.alphaRange.addEventListener("input", () => {
      this.alphaValue.textContent = this.alphaRange.value;
    });

    // 文件选择变化时的提示
    this.fileInput.addEventListener("change", () => {
      const files = this.fileInput.files;
      if (files.length > 0) {
        this.showUploadStatus(`已选择 ${files.length} 个文件`, "info");
      }
    });

    // 初始化alpha控制显示状态
    if (this.modeSelect.value === "hybrid") {
      this.alphaControl.style.display = "block";
    }
  }

  showWelcomeMessage() {
    this.welcomeMessage.style.display = "block";
    this.searchResults.innerHTML = "";
    this.searchInfo.textContent = "";
  }

  hideWelcomeMessage() {
    this.welcomeMessage.style.display = "none";
  }

  showLoading() {
    this.loadingIndicator.classList.remove("d-none");
    this.hideWelcomeMessage();
  }

  hideLoading() {
    this.loadingIndicator.classList.add("d-none");
  }

  showUploadStatus(message, type = "info") {
    const alertClass =
      type === "success"
        ? "alert-success"
        : type === "error"
        ? "alert-danger"
        : "alert-info";

    this.uploadStatus.innerHTML = `
            <div class="alert ${alertClass} alert-custom" role="alert">
                <i class="fas fa-${
                  type === "success"
                    ? "check-circle"
                    : type === "error"
                    ? "exclamation-circle"
                    : "info-circle"
                }"></i>
                ${message}
            </div>
        `;

    // 3秒后自动清除状态信息
    setTimeout(() => {
      this.uploadStatus.innerHTML = "";
    }, 3000);
  }

  async loadStats() {
    try {
      const response = await fetch("/stats");
      if (response.ok) {
        const stats = await response.json();
        console.log("系统统计:", stats);
      }
    } catch (error) {
      console.log("无法加载统计信息:", error);
    }
  }

  async uploadFiles() {
    const files = this.fileInput.files;

    if (files.length === 0) {
      this.showUploadStatus("请先选择文件", "error");
      return;
    }

    // 检查文件类型
    for (let file of files) {
      if (!file.name.endsWith(".txt") && !file.name.endsWith(".md")) {
        this.showUploadStatus("只支持 .txt 和 .md 文件", "error");
        return;
      }
    }

    const formData = new FormData();
    for (let file of files) {
      formData.append("files", file);
    }

    this.uploadBtn.disabled = true;
    this.uploadBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin"></i> 上传中...';

    try {
      const response = await fetch("/upload", {
        method: "POST",
        body: formData,
      });

      const result = await response.json();

      if (response.ok) {
        this.showUploadStatus(result.message, "success");
        this.fileInput.value = ""; // 清空文件选择
        this.loadStats(); // 重新加载统计信息
      } else {
        this.showUploadStatus(result.detail || "上传失败", "error");
      }
    } catch (error) {
      console.error("Upload error:", error);
      this.showUploadStatus("网络错误，请重试", "error");
    } finally {
      this.uploadBtn.disabled = false;
      this.uploadBtn.innerHTML =
        '<i class="fas fa-cloud-upload-alt"></i> 上传文档';
    }
  }

  async searchDocuments() {
    const query = this.searchInput.value.trim();

    if (!query) {
      this.searchInput.focus();
      return;
    }

    const limit = parseInt(this.limitSelect.value);
    const mode = this.modeSelect.value;
    const alpha = mode === "hybrid" ? parseFloat(this.alphaRange.value) : 0.5;

    this.searchBtn.disabled = true;
    this.searchBtn.innerHTML =
      '<i class="fas fa-spinner fa-spin"></i> 搜索中...';
    this.showLoading();

    try {
      const response = await fetch("/search", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          query: query,
          limit: limit,
          mode: mode,
          alpha: alpha,
        }),
      });

      const result = await response.json();

      if (response.ok) {
        this.displaySearchResults(result);
      } else {
        this.showSearchError(result.detail || "搜索失败");
      }
    } catch (error) {
      console.error("Search error:", error);
      this.showSearchError("网络错误，请重试");
    } finally {
      this.searchBtn.disabled = false;
      this.searchBtn.innerHTML = '<i class="fas fa-search"></i> 搜索';
      this.hideLoading();
    }
  }

  displaySearchResults(result) {
    this.hideWelcomeMessage();

    // 更新搜索信息
    let modeText = this.getModeText(result.mode);
    let alphaText = result.alpha !== null ? ` (权重: ${result.alpha})` : "";

    this.searchInfo.innerHTML = `
            搜索 "<strong>${result.query}</strong>" 找到 <strong>${result.total}</strong> 条结果
            <br><small class="text-muted">搜索模式: ${modeText}${alphaText}</small>
        `;

    if (result.results.length === 0) {
      this.searchResults.innerHTML = `
                <div class="alert alert-warning alert-custom" role="alert">
                    <i class="fas fa-exclamation-triangle"></i>
                    没有找到相关文档，请尝试其他关键词或搜索模式
                </div>
            `;
      return;
    }

    // 显示搜索结果
    const resultsHtml = result.results
      .map(
        (item, index) => `
            <div class="search-result-item">
                <div class="result-header">
                    <h6 class="mb-0">结果 ${index + 1}</h6>
                    <div>
                        <span class="result-score">相关度: ${item.score.toFixed(
                          3
                        )}</span>
                        <span class="badge bg-secondary ms-2">${this.getModeText(
                          item.mode
                        )}</span>
                    </div>
                </div>
                <div class="result-source">
                    <i class="fas fa-file-text"></i> ${item.source}
                </div>
                <div class="result-text">
                    ${this.highlightQuery(item.text, result.query)}
                </div>
            </div>
        `
      )
      .join("");

    this.searchResults.innerHTML = resultsHtml;
  }

  getModeText(mode) {
    const modeMap = {
      hybrid: "混合搜索",
      sparse: "稀疏向量",
      dense: "密向量",
      bm25: "BM25",
    };
    return modeMap[mode] || mode;
  }

  highlightQuery(text, query) {
    // 简单的关键词高亮
    const keywords = query.split(/\s+/);
    let highlightedText = text;

    keywords.forEach((keyword) => {
      if (keyword.length > 0) {
        const regex = new RegExp(`(${keyword})`, "gi");
        highlightedText = highlightedText.replace(
          regex,
          '<span class="highlight">$1</span>'
        );
      }
    });

    return highlightedText;
  }

  showSearchError(message) {
    this.hideWelcomeMessage();
    this.searchInfo.textContent = "";
    this.searchResults.innerHTML = `
            <div class="alert alert-danger alert-custom" role="alert">
                <i class="fas fa-exclamation-circle"></i>
                ${message}
            </div>
        `;
  }
}

// 页面加载完成后初始化应用
document.addEventListener("DOMContentLoaded", () => {
  new HybridSearchApp();
});
```

## 第七步：使用说明和最佳实践

### 7.1 快速开始

```bash
# 1. 克隆或创建项目目录
mkdir chinese_bm25_search && cd chinese_bm25_search

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动 Qdrant（使用 Docker）
docker run -p 6333:6333 qdrant/qdrant:latest

# 4. 启动混合检索应用
python hybrid_main.py

# 5. 访问 http://localhost:8000
```

### 7.2 功能特性

- **四种搜索模式**：

  - **混合搜索**：结合稀疏向量(jieba+TF-IDF)和密向量的优势，可调节权重
  - **稀疏向量搜索**：基于 jieba 分词和 TF-IDF 的关键词匹配
  - **密向量搜索**：基于 BGE 中文嵌入模型的语义相似度搜索
  - **BM25 搜索**：传统 BM25 算法的备用方案

- **中文优化**：

  - jieba 分词支持
  - 中文句子分割
  - BGE 中文嵌入模型
  - TF-IDF 稀疏向量生成

- **现代架构**：

  - FastAPI 异步框架
  - Qdrant 高性能向量数据库
  - LlamaIndex 统一接口
  - scikit-learn TF-IDF
  - sentence-transformers 嵌入模型

- **交互式界面**：
  - 实时搜索模式切换
  - 混合搜索权重调节
  - 搜索结果高亮显示
  - 系统统计信息

### 7.3 性能优化建议

1. **文档分块**：

   - 调整 `chunk_size` 参数（推荐 512-1024）
   - 设置合适的 `chunk_overlap`（推荐 50-100）

2. **索引优化**：

   - 使用批量上传减少网络开销
   - 定期清理无用的向量数据

3. **搜索优化**：
   - 根据场景选择合适的搜索模式
   - 调整 `top_k` 参数平衡精度和性能

### 7.4 扩展建议

1. **添加更多文件格式支持**：

   - PDF 文档解析
   - Word 文档支持
   - HTML 内容提取

2. **增强搜索功能**：

   - 搜索历史记录
   - 搜索结果导出
   - 高级过滤选项

3. **用户体验优化**：
   - 搜索建议
   - 结果预览
   - 响应式设计改进

## 总结

本教程提供了一个完整的中文文档混合检索系统实现，具有以下优势：

### 🎯 核心特性
- **真正的混合检索**：结合jieba+TF-IDF稀疏向量和BGE密向量
- **四种搜索模式**：混合、稀疏、密向量、BM25，满足不同场景需求
- **中文优化**：专门针对中文文档的分词、嵌入和检索优化
- **权重可调**：混合搜索中可以实时调节稀疏和密向量的权重

### 🚀 技术优势
- **基于官方最佳实践**：使用LlamaIndex和Qdrant的推荐方法
- **高性能架构**：异步FastAPI + 高效向量数据库
- **模块化设计**：易于扩展和定制
- **现代化界面**：Bootstrap + JavaScript交互式前端

### 📊 实际效果
- **关键词匹配**：稀疏向量确保精确的关键词匹配
- **语义理解**：密向量提供语义相似度搜索
- **最佳平衡**：混合搜索结合两者优势，提供最佳检索效果
- **实时调优**：可根据查询类型动态调整搜索策略

### 🛠️ 快速部署
```bash
# 一键启动完整系统
git clone <your-repo>
cd chinese_hybrid_search
pip install -r requirements.txt
docker run -p 6333:6333 qdrant/qdrant:latest
python hybrid_main.py
```

通过这个系统，你可以快速构建一个生产级的中文文档混合检索应用，既有传统关键词搜索的精确性，又有现代语义搜索的智能性。
