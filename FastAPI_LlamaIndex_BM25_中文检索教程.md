# FastAPI + LlamaIndex + jieba + BM25 中文文档检索项目教程

## 项目概述

本教程将指导你创建一个基于 FastAPI、LlamaIndex、jieba 分词和 BM25 算法的中文文档检索系统，包含完整的前端展示页面。

## 技术栈

- **后端**: FastAPI + LlamaIndex + jieba + rank-bm25
- **数据库**: Qdrant (稀疏向量存储)
- **前端**: HTML + Bootstrap + JavaScript
- **分词**: jieba (中文分词)
- **检索算法**: BM25

## 项目结构

```
project/
├── app/
│   ├── main.py              # FastAPI 主应用
│   ├── models.py            # 数据模型
│   ├── services/
│   │   ├── bm25_service.py  # BM25 检索服务
│   │   └── document_service.py # 文档处理服务
│   └── static/
│       ├── index.html       # 前端页面
│       ├── css/
│       │   └── style.css
│       └── js/
│           └── app.js
├── data/
│   └── documents/           # 存放待检索的文档
├── requirements.txt
└── README.md
```

## 第一步：环境准备

### 1.1 安装依赖

```bash
pip install fastapi uvicorn
pip install llama-index-core llama-index-retrievers-bm25
pip install qdrant-client>=1.7
pip install jieba rank-bm25
pip install python-multipart  # 文件上传支持
```

### 1.2 启动 Qdrant

```bash
# 使用 Docker 启动 Qdrant
docker run -p 6333:6333 qdrant/qdrant
```

## 第二步：后端实现

### 2.1 数据模型 (models.py)

```python
from pydantic import BaseModel
from typing import List, Optional

class DocumentUpload(BaseModel):
    filename: str
    content: str

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 5

class SearchResult(BaseModel):
    text: str
    score: float
    source: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
```

### 2.2 BM25 检索服务 (services/bm25_service.py)

```python
import jieba
from rank_bm25 import BM25Okapi
from qdrant_client import QdrantClient, models
from typing import List, Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class BM25Service:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.client = QdrantClient(url=qdrant_url)
        self.collection_name = "chinese_documents_bm25"
        self.bm25 = None
        self.vocab = {}
        self.ensure_collection()

    def ensure_collection(self):
        """确保 Qdrant collection 存在"""
        collections = {c.name for c in self.client.get_collections().collections}
        if self.collection_name not in collections:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config={},  # 无密集向量
                sparse_vectors_config={
                    "bm25": models.SparseVectorParams(
                        index=models.SparseIndexParams()
                    )
                }
            )
            logger.info(f"Created collection: {self.collection_name}")

    def tokenize_text(self, text: str) -> List[str]:
        """中文分词"""
        return jieba.lcut(text)

    def build_bm25_index(self, documents: List[str]) -> None:
        """构建 BM25 索引"""
        token_lists = [self.tokenize_text(doc) for doc in documents]
        self.bm25 = BM25Okapi(token_lists)
        self.vocab = {token: idx for idx, token in enumerate(self.bm25._doc_freqs)}
        logger.info(f"Built BM25 index with {len(self.vocab)} tokens")

    def to_sparse_vector(self, tokens: List[str]) -> Tuple[List[int], List[float]]:
        """将分词结果转换为稀疏向量"""
        if not self.bm25 or not self.vocab:
            return [], []

        tf = {}
        for token in tokens:
            tf[token] = tf.get(token, 0) + 1

        indices, values = [], []
        for token, count in tf.items():
            if token not in self.vocab:
                continue

            idf = self.bm25.idf.get(token, 0)
            weight = idf * count * (self.bm25.k1 + 1) / (count + self.bm25.k1)

            indices.append(self.vocab[token])
            values.append(float(weight))

        return indices, values

    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到索引"""
        try:
            # 重新构建 BM25 索引
            texts = [doc["content"] for doc in documents]
            self.build_bm25_index(texts)

            # 生成稀疏向量并存储到 Qdrant
            batch = []
            for idx, doc in enumerate(documents):
                tokens = self.tokenize_text(doc["content"])
                indices, values = self.to_sparse_vector(tokens)

                point = models.PointStruct(
                    id=idx,
                    vector={"bm25": models.SparseVector(indices=indices, values=values)},
                    payload={
                        "text": doc["content"],
                        "source": doc.get("filename", f"doc_{idx}"),
                        "tokens": len(tokens)
                    }
                )
                batch.append(point)

            # 批量插入
            if batch:
                self.client.upsert(self.collection_name, batch)
                logger.info(f"Added {len(batch)} documents to collection")

            return True
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return False

    def search(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            if not self.bm25 or not self.vocab:
                return []

            # 对查询进行分词和向量化
            query_tokens = self.tokenize_text(query)
            query_indices, query_values = self.to_sparse_vector(query_tokens)

            if not query_indices:
                return []

            # 在 Qdrant 中搜索
            search_result = self.client.search(
                collection_name=self.collection_name,
                sparse_vector=models.SparseVector(
                    indices=query_indices,
                    values=query_values
                ),
                limit=limit,
                with_payload=True
            )

            results = []
            for hit in search_result:
                results.append({
                    "text": hit.payload["text"],
                    "score": hit.score,
                    "source": hit.payload["source"]
                })

            return results
        except Exception as e:
            logger.error(f"Error searching: {e}")
            return []
```

### 2.3 文档处理服务 (services/document_service.py)

```python
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import SentenceSplitter
from typing import List, Dict, Any
import os
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self):
        self.splitter = SentenceSplitter(
            chunk_size=700,
            chunk_overlap=100,
            lang="zh"  # 中文句子分割
        )

    def process_uploaded_file(self, content: str, filename: str) -> List[Dict[str, Any]]:
        """处理上传的文件内容"""
        try:
            # 将内容分割成块
            chunks = self.splitter.split_text(content)

            documents = []
            for i, chunk in enumerate(chunks):
                documents.append({
                    "content": chunk,
                    "filename": f"{filename}_chunk_{i}",
                    "original_file": filename
                })

            logger.info(f"Processed {filename} into {len(documents)} chunks")
            return documents
        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            return []

    def load_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """从目录加载文档"""
        try:
            if not os.path.exists(directory_path):
                return []

            reader = SimpleDirectoryReader(directory_path)
            docs = reader.load_data()

            all_documents = []
            for doc in docs:
                nodes = self.splitter.get_nodes_from_documents([doc])
                for i, node in enumerate(nodes):
                    all_documents.append({
                        "content": node.text,
                        "filename": f"{doc.metadata.get('file_name', 'unknown')}_chunk_{i}",
                        "original_file": doc.metadata.get('file_name', 'unknown')
                    })

            logger.info(f"Loaded {len(all_documents)} document chunks from {directory_path}")
            return all_documents
        except Exception as e:
            logger.error(f"Error loading directory {directory_path}: {e}")
            return []
```

## 第三步：FastAPI 主应用 (main.py)

```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import logging
from typing import List

from models import SearchRequest, SearchResponse, SearchResult
from services.bm25_service import BM25Service
from services.document_service import DocumentService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="中文文档 BM25 检索系统", version="1.0.0")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化服务
bm25_service = BM25Service()
document_service = DocumentService()

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    try:
        all_documents = []

        for file in files:
            if not file.filename.endswith(('.txt', '.md')):
                raise HTTPException(status_code=400, detail="只支持 .txt 和 .md 文件")

            content = await file.read()
            content = content.decode('utf-8')

            documents = document_service.process_uploaded_file(content, file.filename)
            all_documents.extend(documents)

        # 添加到 BM25 索引
        success = bm25_service.add_documents(all_documents)

        if success:
            return {
                "message": f"成功上传并处理了 {len(files)} 个文件，生成 {len(all_documents)} 个文档块",
                "document_count": len(all_documents)
            }
        else:
            raise HTTPException(status_code=500, detail="文档处理失败")

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """搜索文档"""
    try:
        results = bm25_service.search(request.query, request.limit)

        search_results = [
            SearchResult(
                text=result["text"][:200] + "..." if len(result["text"]) > 200 else result["text"],
                score=result["score"],
                source=result["source"]
            )
            for result in results
        ]

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query
        )
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "BM25 中文检索系统"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 第四步：前端页面实现

### 4.1 HTML 页面 (static/index.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中文文档 BM25 检索系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3 sidebar">
          <div class="sidebar-header">
            <h4><i class="fas fa-search"></i> BM25 检索系统</h4>
          </div>

          <!-- 文件上传区域 -->
          <div class="upload-section">
            <h5><i class="fas fa-upload"></i> 文档上传</h5>
            <div class="mb-3">
              <input
                type="file"
                class="form-control"
                id="fileInput"
                multiple
                accept=".txt,.md"
              />
            </div>
            <button type="button" class="btn btn-primary w-100" id="uploadBtn">
              <i class="fas fa-cloud-upload-alt"></i> 上传文档
            </button>
            <div id="uploadStatus" class="mt-2"></div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-section">
            <h5><i class="fas fa-search"></i> 文档搜索</h5>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="searchInput"
                placeholder="输入搜索关键词..."
              />
            </div>
            <div class="mb-3">
              <label for="limitSelect" class="form-label">结果数量:</label>
              <select class="form-select" id="limitSelect">
                <option value="5">5 条</option>
                <option value="10">10 条</option>
                <option value="20">20 条</option>
              </select>
            </div>
            <button type="button" class="btn btn-success w-100" id="searchBtn">
              <i class="fas fa-search"></i> 搜索
            </button>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="col-md-9 main-content">
          <div class="content-header">
            <h2>检索结果</h2>
            <div id="searchInfo" class="text-muted"></div>
          </div>

          <!-- 加载指示器 -->
          <div id="loadingIndicator" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理...</p>
          </div>

          <!-- 搜索结果 -->
          <div id="searchResults"></div>

          <!-- 欢迎信息 -->
          <div id="welcomeMessage" class="welcome-message">
            <div class="text-center">
              <i class="fas fa-file-text fa-5x text-muted mb-4"></i>
              <h3>欢迎使用 BM25 中文文档检索系统</h3>
              <p class="lead">请先上传文档，然后进行搜索</p>
              <div class="features">
                <div class="row">
                  <div class="col-md-4">
                    <i class="fas fa-language fa-2x text-primary"></i>
                    <h5>中文分词</h5>
                    <p>使用 jieba 进行精确的中文分词</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-bolt fa-2x text-warning"></i>
                    <h5>BM25 算法</h5>
                    <p>基于 BM25 的高效文档检索</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-database fa-2x text-success"></i>
                    <h5>向量存储</h5>
                    <p>使用 Qdrant 稀疏向量存储</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
  </body>
</html>
```

### 4.2 CSS 样式 (static/css/style.css)

```css
body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.sidebar {
  background-color: #343a40;
  color: white;
  min-height: 100vh;
  padding: 20px;
}

.sidebar-header {
  border-bottom: 1px solid #495057;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.sidebar-header h4 {
  margin: 0;
  color: #fff;
}

.upload-section,
.search-section {
  background-color: #495057;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.upload-section h5,
.search-section h5 {
  color: #fff;
  margin-bottom: 15px;
  border-bottom: 1px solid #6c757d;
  padding-bottom: 8px;
}

.main-content {
  padding: 20px;
}

.content-header {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.search-result-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.search-result-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-score {
  background-color: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
}

.result-source {
  color: #6c757d;
  font-size: 0.9em;
  margin-bottom: 8px;
}

.result-text {
  line-height: 1.6;
  color: #333;
}

.welcome-message {
  text-align: center;
  padding: 60px 20px;
}

.features {
  margin-top: 40px;
}

.features .col-md-4 {
  padding: 20px;
}

.features h5 {
  margin: 15px 0 10px 0;
  color: #333;
}

.features p {
  color: #6c757d;
  font-size: 0.9em;
}

.alert-custom {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#uploadStatus .alert {
  margin-top: 10px;
  margin-bottom: 0;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.form-control,
.form-select {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    min-height: auto;
    margin-bottom: 20px;
  }

  .main-content {
    padding: 15px;
  }

  .welcome-message {
    padding: 30px 15px;
  }

  .features .col-md-4 {
    margin-bottom: 20px;
  }
}

/* 加载动画 */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* 搜索结果高亮 */
.highlight {
  background-color: #fff3cd;
  padding: 2px 4px;
  border-radius: 3px;
}
```

## 第五步：优化后的核心代码（基于官方文档最佳实践）

### 5.1 简化的 BM25 服务 (services/simple_bm25_service.py)

```python
import jieba
from llama_index.core import VectorStoreIndex, StorageContext, Document
from llama_index.core.node_parser import SentenceSplitter
from llama_index.vector_stores.qdrant import QdrantVectorStore
from llama_index.retrievers.bm25 import BM25Retriever
from qdrant_client import QdrantClient
from typing import List, Dict, Any
import logging

logger = logging.getLogger(__name__)

class SimpleBM25Service:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.client = QdrantClient(url=qdrant_url)
        self.collection_name = "chinese_bm25_docs"
        self.bm25_retriever = None
        self.vector_store = None
        self.index = None
        self.setup_vector_store()

    def setup_vector_store(self):
        """设置向量存储"""
        # 使用 Qdrant 的混合搜索功能
        self.vector_store = QdrantVectorStore(
            client=self.client,
            collection_name=self.collection_name,
            enable_hybrid=True,  # 启用混合搜索
            fastembed_sparse_model="Qdrant/bm25",  # 使用内置 BM25
        )

    def add_documents_from_files(self, file_contents: List[Dict[str, str]]) -> bool:
        """从文件内容添加文档"""
        try:
            # 创建文档对象
            documents = []

            for file_data in file_contents:
                # 使用 jieba 进行预处理（可选）
                content = file_data["content"]
                # 这里可以添加 jieba 分词预处理
                # content = " ".join(jieba.cut(content))

                doc = Document(
                    text=content,
                    metadata={"filename": file_data["filename"]}
                )
                documents.append(doc)

            # 使用 LlamaIndex 的文档分割器
            splitter = SentenceSplitter(
                chunk_size=512,
                chunk_overlap=50,
                lang="zh"  # 中文支持
            )

            # 创建存储上下文
            storage_context = StorageContext.from_defaults(
                vector_store=self.vector_store
            )

            # 创建索引（自动处理嵌入和存储）
            self.index = VectorStoreIndex.from_documents(
                documents,
                storage_context=storage_context,
                transformations=[splitter]
            )

            # 创建 BM25 检索器
            nodes = splitter.get_nodes_from_documents(documents)
            self.bm25_retriever = BM25Retriever.from_defaults(
                nodes=nodes,
                similarity_top_k=10
            )

            logger.info(f"Successfully processed {len(documents)} documents")
            return True

        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return False

    def search(self, query: str, limit: int = 5, mode: str = "hybrid") -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            if not self.index:
                return []

            if mode == "bm25" and self.bm25_retriever:
                # 纯 BM25 搜索
                # 对查询进行 jieba 分词
                query_tokens = " ".join(jieba.cut(query))
                nodes = self.bm25_retriever.retrieve(query_tokens)

                results = []
                for node in nodes[:limit]:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown")
                    })
                return results

            elif mode == "hybrid":
                # 混合搜索（向量 + BM25）
                retriever = self.index.as_retriever(
                    vector_store_query_mode="hybrid",
                    sparse_top_k=limit,
                    similarity_top_k=limit,
                    hybrid_top_k=limit,
                )

                nodes = retriever.retrieve(query)

                results = []
                for node in nodes:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown")
                    })
                return results

            else:
                # 纯向量搜索
                retriever = self.index.as_retriever(similarity_top_k=limit)
                nodes = retriever.retrieve(query)

                results = []
                for node in nodes:
                    results.append({
                        "text": node.text,
                        "score": node.score or 0.0,
                        "source": node.metadata.get("filename", "unknown")
                    })
                return results

        except Exception as e:
            logger.error(f"Error searching: {e}")
            return []
```

### 5.2 简化的 FastAPI 主应用 (simple_main.py)

```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
from pydantic import BaseModel
from typing import List, Optional
import uvicorn
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="简化版中文文档 BM25 检索系统", version="1.0.0")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 导入简化的服务
from services.simple_bm25_service import SimpleBM25Service

# 初始化服务
bm25_service = SimpleBM25Service()

# 数据模型
class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 5
    mode: Optional[str] = "hybrid"  # "bm25", "vector", "hybrid"

class SearchResult(BaseModel):
    text: str
    score: float
    source: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
    mode: str

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    try:
        file_contents = []

        for file in files:
            if not file.filename.endswith(('.txt', '.md')):
                raise HTTPException(status_code=400, detail="只支持 .txt 和 .md 文件")

            content = await file.read()
            content = content.decode('utf-8')

            file_contents.append({
                "content": content,
                "filename": file.filename
            })

        # 添加到索引
        success = bm25_service.add_documents_from_files(file_contents)

        if success:
            return {
                "message": f"成功上传并处理了 {len(files)} 个文件",
                "file_count": len(files)
            }
        else:
            raise HTTPException(status_code=500, detail="文档处理失败")

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """搜索文档"""
    try:
        results = bm25_service.search(
            request.query,
            request.limit,
            request.mode
        )

        search_results = [
            SearchResult(
                text=result["text"][:300] + "..." if len(result["text"]) > 300 else result["text"],
                score=result["score"],
                source=result["source"]
            )
            for result in results
        ]

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query,
            mode=request.mode
        )
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "简化版 BM25 中文检索系统"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 第六步：项目配置和启动

### 6.1 更新的 requirements.txt

```txt
fastapi==0.104.1
uvicorn[standard]==0.24.0
llama-index-core==0.10.57
llama-index-vector-stores-qdrant==0.2.8
llama-index-retrievers-bm25==0.1.3
qdrant-client>=1.7.0
jieba==0.42.1
python-multipart==0.0.6
```

### 6.2 Docker Compose 配置 (docker-compose.yml)

```yaml
version: "3.8"

services:
  qdrant:
    image: qdrant/qdrant:latest
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    environment:
      - QDRANT__SERVICE__HTTP_PORT=6333
      - QDRANT__SERVICE__GRPC_PORT=6334

volumes:
  qdrant_data:
```

### 6.3 启动脚本 (start.py)

```python
import subprocess
import sys
import os
import time

def check_qdrant():
    """检查 Qdrant 是否运行"""
    try:
        import requests
        response = requests.get("http://localhost:6333/health")
        return response.status_code == 200
    except:
        return False

def start_qdrant():
    """启动 Qdrant"""
    print("启动 Qdrant...")
    subprocess.run(["docker-compose", "up", "-d", "qdrant"])

    # 等待 Qdrant 启动
    for i in range(30):
        if check_qdrant():
            print("Qdrant 启动成功!")
            return True
        time.sleep(1)
        print(f"等待 Qdrant 启动... ({i+1}/30)")

    print("Qdrant 启动失败!")
    return False

def main():
    # 确保必要的目录存在
    os.makedirs("static/css", exist_ok=True)
    os.makedirs("static/js", exist_ok=True)
    os.makedirs("services", exist_ok=True)

    # 启动 Qdrant
    if not check_qdrant():
        if not start_qdrant():
            sys.exit(1)
    else:
        print("Qdrant 已经在运行")

    print("启动 FastAPI 应用...")
    print("访问地址: http://localhost:8000")

    # 启动 FastAPI
    subprocess.run([
        sys.executable, "-m", "uvicorn",
        "simple_main:app",
        "--host", "0.0.0.0",
        "--port", "8000",
        "--reload"
    ])

if __name__ == "__main__":
    main()
```

### 6.4 前端页面增强 (static/index_enhanced.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中文文档 BM25 检索系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3 sidebar">
          <div class="sidebar-header">
            <h4><i class="fas fa-search"></i> BM25 检索系统</h4>
          </div>

          <!-- 文件上传区域 -->
          <div class="upload-section">
            <h5><i class="fas fa-upload"></i> 文档上传</h5>
            <div class="mb-3">
              <input
                type="file"
                class="form-control"
                id="fileInput"
                multiple
                accept=".txt,.md"
              />
            </div>
            <button type="button" class="btn btn-primary w-100" id="uploadBtn">
              <i class="fas fa-cloud-upload-alt"></i> 上传文档
            </button>
            <div id="uploadStatus" class="mt-2"></div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-section">
            <h5><i class="fas fa-search"></i> 文档搜索</h5>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="searchInput"
                placeholder="输入搜索关键词..."
              />
            </div>
            <div class="mb-3">
              <label for="limitSelect" class="form-label">结果数量:</label>
              <select class="form-select" id="limitSelect">
                <option value="5">5 条</option>
                <option value="10">10 条</option>
                <option value="20">20 条</option>
              </select>
            </div>
            <div class="mb-3">
              <label for="modeSelect" class="form-label">搜索模式:</label>
              <select class="form-select" id="modeSelect">
                <option value="hybrid">混合搜索</option>
                <option value="bm25">BM25 搜索</option>
                <option value="vector">向量搜索</option>
              </select>
            </div>
            <button type="button" class="btn btn-success w-100" id="searchBtn">
              <i class="fas fa-search"></i> 搜索
            </button>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="col-md-9 main-content">
          <div class="content-header">
            <h2>检索结果</h2>
            <div id="searchInfo" class="text-muted"></div>
          </div>

          <!-- 加载指示器 -->
          <div id="loadingIndicator" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理...</p>
          </div>

          <!-- 搜索结果 -->
          <div id="searchResults"></div>

          <!-- 欢迎信息 -->
          <div id="welcomeMessage" class="welcome-message">
            <div class="text-center">
              <i class="fas fa-file-text fa-5x text-muted mb-4"></i>
              <h3>欢迎使用 BM25 中文文档检索系统</h3>
              <p class="lead">请先上传文档，然后进行搜索</p>
              <div class="features">
                <div class="row">
                  <div class="col-md-4">
                    <i class="fas fa-language fa-2x text-primary"></i>
                    <h5>中文分词</h5>
                    <p>使用 jieba 进行精确的中文分词</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-bolt fa-2x text-warning"></i>
                    <h5>混合搜索</h5>
                    <p>BM25 + 向量搜索的最佳组合</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-database fa-2x text-success"></i>
                    <h5>Qdrant 存储</h5>
                    <p>高性能向量数据库</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/enhanced_app.js"></script>
  </body>
</html>
```

## 第七步：使用说明和最佳实践

### 7.1 快速开始

```bash
# 1. 克隆或创建项目目录
mkdir chinese_bm25_search && cd chinese_bm25_search

# 2. 安装依赖
pip install -r requirements.txt

# 3. 启动 Qdrant（使用 Docker）
docker run -p 6333:6333 qdrant/qdrant:latest

# 4. 启动应用
python simple_main.py

# 5. 访问 http://localhost:8000
```

### 7.2 功能特性

- **三种搜索模式**：

  - **混合搜索**：结合 BM25 和向量搜索的优势
  - **BM25 搜索**：基于关键词的精确匹配
  - **向量搜索**：基于语义相似度的搜索

- **中文优化**：

  - jieba 分词支持
  - 中文句子分割
  - 中文停用词处理

- **现代架构**：
  - FastAPI 异步框架
  - Qdrant 高性能向量数据库
  - LlamaIndex 统一接口

### 7.3 性能优化建议

1. **文档分块**：

   - 调整 `chunk_size` 参数（推荐 512-1024）
   - 设置合适的 `chunk_overlap`（推荐 50-100）

2. **索引优化**：

   - 使用批量上传减少网络开销
   - 定期清理无用的向量数据

3. **搜索优化**：
   - 根据场景选择合适的搜索模式
   - 调整 `top_k` 参数平衡精度和性能

### 7.4 扩展建议

1. **添加更多文件格式支持**：

   - PDF 文档解析
   - Word 文档支持
   - HTML 内容提取

2. **增强搜索功能**：

   - 搜索历史记录
   - 搜索结果导出
   - 高级过滤选项

3. **用户体验优化**：
   - 搜索建议
   - 结果预览
   - 响应式设计改进

## 总结

本教程提供了一个完整的中文文档 BM25 检索系统实现，具有以下优势：

- **简单易用**：基于官方最佳实践，代码简洁
- **功能完整**：支持文档上传、分词、索引、搜索
- **性能优秀**：使用 Qdrant 和 LlamaIndex 的高效实现
- **扩展性强**：模块化设计，易于扩展和定制

通过这个系统，你可以快速构建一个高质量的中文文档检索应用。
