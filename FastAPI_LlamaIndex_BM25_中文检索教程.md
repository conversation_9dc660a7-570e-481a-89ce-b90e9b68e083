# FastAPI + LlamaIndex + jieba + BM25 中文文档检索项目教程

## 项目概述

本教程将指导你创建一个基于 FastAPI、LlamaIndex、jieba 分词和 BM25 算法的中文文档检索系统，包含完整的前端展示页面。

## 技术栈

- **后端**: FastAPI + LlamaIndex + jieba + rank-bm25
- **数据库**: Qdrant (稀疏向量存储)
- **前端**: HTML + Bootstrap + JavaScript
- **分词**: jieba (中文分词)
- **检索算法**: BM25

## 项目结构

```
project/
├── app/
│   ├── main.py              # FastAPI 主应用
│   ├── models.py            # 数据模型
│   ├── services/
│   │   ├── bm25_service.py  # BM25 检索服务
│   │   └── document_service.py # 文档处理服务
│   └── static/
│       ├── index.html       # 前端页面
│       ├── css/
│       │   └── style.css
│       └── js/
│           └── app.js
├── data/
│   └── documents/           # 存放待检索的文档
├── requirements.txt
└── README.md
```

## 第一步：环境准备

### 1.1 安装依赖

```bash
pip install fastapi uvicorn
pip install llama-index-core llama-index-retrievers-bm25
pip install qdrant-client>=1.7
pip install jieba rank-bm25
pip install python-multipart  # 文件上传支持
```

### 1.2 启动 Qdrant

```bash
# 使用 Docker 启动 Qdrant
docker run -p 6333:6333 qdrant/qdrant
```

## 第二步：后端实现

### 2.1 数据模型 (models.py)

```python
from pydantic import BaseModel
from typing import List, Optional

class DocumentUpload(BaseModel):
    filename: str
    content: str

class SearchRequest(BaseModel):
    query: str
    limit: Optional[int] = 5

class SearchResult(BaseModel):
    text: str
    score: float
    source: str

class SearchResponse(BaseModel):
    results: List[SearchResult]
    total: int
    query: str
```

### 2.2 BM25 检索服务 (services/bm25_service.py)

```python
import jieba
from rank_bm25 import BM25Okapi
from qdrant_client import QdrantClient, models
from typing import List, Tuple, Dict, Any
import logging

logger = logging.getLogger(__name__)

class BM25Service:
    def __init__(self, qdrant_url: str = "http://localhost:6333"):
        self.client = QdrantClient(url=qdrant_url)
        self.collection_name = "chinese_documents_bm25"
        self.bm25 = None
        self.vocab = {}
        self.ensure_collection()

    def ensure_collection(self):
        """确保 Qdrant collection 存在"""
        collections = {c.name for c in self.client.get_collections().collections}
        if self.collection_name not in collections:
            self.client.create_collection(
                collection_name=self.collection_name,
                vectors_config={},  # 无密集向量
                sparse_vectors_config={
                    "bm25": models.SparseVectorParams(
                        index=models.SparseIndexParams()
                    )
                }
            )
            logger.info(f"Created collection: {self.collection_name}")

    def tokenize_text(self, text: str) -> List[str]:
        """中文分词"""
        return jieba.lcut(text)

    def build_bm25_index(self, documents: List[str]) -> None:
        """构建 BM25 索引"""
        token_lists = [self.tokenize_text(doc) for doc in documents]
        self.bm25 = BM25Okapi(token_lists)
        self.vocab = {token: idx for idx, token in enumerate(self.bm25._doc_freqs)}
        logger.info(f"Built BM25 index with {len(self.vocab)} tokens")

    def to_sparse_vector(self, tokens: List[str]) -> Tuple[List[int], List[float]]:
        """将分词结果转换为稀疏向量"""
        if not self.bm25 or not self.vocab:
            return [], []

        tf = {}
        for token in tokens:
            tf[token] = tf.get(token, 0) + 1

        indices, values = [], []
        for token, count in tf.items():
            if token not in self.vocab:
                continue

            idf = self.bm25.idf.get(token, 0)
            weight = idf * count * (self.bm25.k1 + 1) / (count + self.bm25.k1)

            indices.append(self.vocab[token])
            values.append(float(weight))

        return indices, values

    def add_documents(self, documents: List[Dict[str, Any]]) -> bool:
        """添加文档到索引"""
        try:
            # 重新构建 BM25 索引
            texts = [doc["content"] for doc in documents]
            self.build_bm25_index(texts)

            # 生成稀疏向量并存储到 Qdrant
            batch = []
            for idx, doc in enumerate(documents):
                tokens = self.tokenize_text(doc["content"])
                indices, values = self.to_sparse_vector(tokens)

                point = models.PointStruct(
                    id=idx,
                    vector={"bm25": models.SparseVector(indices=indices, values=values)},
                    payload={
                        "text": doc["content"],
                        "source": doc.get("filename", f"doc_{idx}"),
                        "tokens": len(tokens)
                    }
                )
                batch.append(point)

            # 批量插入
            if batch:
                self.client.upsert(self.collection_name, batch)
                logger.info(f"Added {len(batch)} documents to collection")

            return True
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            return False

    def search(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """搜索文档"""
        try:
            if not self.bm25 or not self.vocab:
                return []

            # 对查询进行分词和向量化
            query_tokens = self.tokenize_text(query)
            query_indices, query_values = self.to_sparse_vector(query_tokens)

            if not query_indices:
                return []

            # 在 Qdrant 中搜索
            search_result = self.client.search(
                collection_name=self.collection_name,
                sparse_vector=models.SparseVector(
                    indices=query_indices,
                    values=query_values
                ),
                limit=limit,
                with_payload=True
            )

            results = []
            for hit in search_result:
                results.append({
                    "text": hit.payload["text"],
                    "score": hit.score,
                    "source": hit.payload["source"]
                })

            return results
        except Exception as e:
            logger.error(f"Error searching: {e}")
            return []
```

### 2.3 文档处理服务 (services/document_service.py)

```python
from llama_index.core import SimpleDirectoryReader
from llama_index.core.node_parser import SentenceSplitter
from typing import List, Dict, Any
import os
import logging

logger = logging.getLogger(__name__)

class DocumentService:
    def __init__(self):
        self.splitter = SentenceSplitter(
            chunk_size=700,
            chunk_overlap=100,
            lang="zh"  # 中文句子分割
        )

    def process_uploaded_file(self, content: str, filename: str) -> List[Dict[str, Any]]:
        """处理上传的文件内容"""
        try:
            # 将内容分割成块
            chunks = self.splitter.split_text(content)

            documents = []
            for i, chunk in enumerate(chunks):
                documents.append({
                    "content": chunk,
                    "filename": f"{filename}_chunk_{i}",
                    "original_file": filename
                })

            logger.info(f"Processed {filename} into {len(documents)} chunks")
            return documents
        except Exception as e:
            logger.error(f"Error processing file {filename}: {e}")
            return []

    def load_directory(self, directory_path: str) -> List[Dict[str, Any]]:
        """从目录加载文档"""
        try:
            if not os.path.exists(directory_path):
                return []

            reader = SimpleDirectoryReader(directory_path)
            docs = reader.load_data()

            all_documents = []
            for doc in docs:
                nodes = self.splitter.get_nodes_from_documents([doc])
                for i, node in enumerate(nodes):
                    all_documents.append({
                        "content": node.text,
                        "filename": f"{doc.metadata.get('file_name', 'unknown')}_chunk_{i}",
                        "original_file": doc.metadata.get('file_name', 'unknown')
                    })

            logger.info(f"Loaded {len(all_documents)} document chunks from {directory_path}")
            return all_documents
        except Exception as e:
            logger.error(f"Error loading directory {directory_path}: {e}")
            return []
```

## 第三步：FastAPI 主应用 (main.py)

```python
from fastapi import FastAPI, File, UploadFile, HTTPException
from fastapi.staticfiles import StaticFiles
from fastapi.responses import HTMLResponse
import uvicorn
import logging
from typing import List

from models import SearchRequest, SearchResponse, SearchResult
from services.bm25_service import BM25Service
from services.document_service import DocumentService

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(title="中文文档 BM25 检索系统", version="1.0.0")

# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")

# 初始化服务
bm25_service = BM25Service()
document_service = DocumentService()

@app.get("/", response_class=HTMLResponse)
async def read_root():
    """返回主页"""
    with open("static/index.html", "r", encoding="utf-8") as f:
        return HTMLResponse(content=f.read())

@app.post("/upload")
async def upload_documents(files: List[UploadFile] = File(...)):
    """上传文档"""
    try:
        all_documents = []

        for file in files:
            if not file.filename.endswith(('.txt', '.md')):
                raise HTTPException(status_code=400, detail="只支持 .txt 和 .md 文件")

            content = await file.read()
            content = content.decode('utf-8')

            documents = document_service.process_uploaded_file(content, file.filename)
            all_documents.extend(documents)

        # 添加到 BM25 索引
        success = bm25_service.add_documents(all_documents)

        if success:
            return {
                "message": f"成功上传并处理了 {len(files)} 个文件，生成 {len(all_documents)} 个文档块",
                "document_count": len(all_documents)
            }
        else:
            raise HTTPException(status_code=500, detail="文档处理失败")

    except Exception as e:
        logger.error(f"Upload error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/search", response_model=SearchResponse)
async def search_documents(request: SearchRequest):
    """搜索文档"""
    try:
        results = bm25_service.search(request.query, request.limit)

        search_results = [
            SearchResult(
                text=result["text"][:200] + "..." if len(result["text"]) > 200 else result["text"],
                score=result["score"],
                source=result["source"]
            )
            for result in results
        ]

        return SearchResponse(
            results=search_results,
            total=len(search_results),
            query=request.query
        )
    except Exception as e:
        logger.error(f"Search error: {e}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/health")
async def health_check():
    """健康检查"""
    return {"status": "healthy", "service": "BM25 中文检索系统"}

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)
```

## 第四步：前端页面实现

### 4.1 HTML 页面 (static/index.html)

```html
<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>中文文档 BM25 检索系统</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link href="/static/css/style.css" rel="stylesheet" />
  </head>
  <body>
    <div class="container-fluid">
      <div class="row">
        <!-- 侧边栏 -->
        <div class="col-md-3 sidebar">
          <div class="sidebar-header">
            <h4><i class="fas fa-search"></i> BM25 检索系统</h4>
          </div>

          <!-- 文件上传区域 -->
          <div class="upload-section">
            <h5><i class="fas fa-upload"></i> 文档上传</h5>
            <div class="mb-3">
              <input
                type="file"
                class="form-control"
                id="fileInput"
                multiple
                accept=".txt,.md"
              />
            </div>
            <button type="button" class="btn btn-primary w-100" id="uploadBtn">
              <i class="fas fa-cloud-upload-alt"></i> 上传文档
            </button>
            <div id="uploadStatus" class="mt-2"></div>
          </div>

          <!-- 搜索区域 -->
          <div class="search-section">
            <h5><i class="fas fa-search"></i> 文档搜索</h5>
            <div class="mb-3">
              <input
                type="text"
                class="form-control"
                id="searchInput"
                placeholder="输入搜索关键词..."
              />
            </div>
            <div class="mb-3">
              <label for="limitSelect" class="form-label">结果数量:</label>
              <select class="form-select" id="limitSelect">
                <option value="5">5 条</option>
                <option value="10">10 条</option>
                <option value="20">20 条</option>
              </select>
            </div>
            <button type="button" class="btn btn-success w-100" id="searchBtn">
              <i class="fas fa-search"></i> 搜索
            </button>
          </div>
        </div>

        <!-- 主内容区域 -->
        <div class="col-md-9 main-content">
          <div class="content-header">
            <h2>检索结果</h2>
            <div id="searchInfo" class="text-muted"></div>
          </div>

          <!-- 加载指示器 -->
          <div id="loadingIndicator" class="text-center d-none">
            <div class="spinner-border text-primary" role="status">
              <span class="visually-hidden">加载中...</span>
            </div>
            <p class="mt-2">正在处理...</p>
          </div>

          <!-- 搜索结果 -->
          <div id="searchResults"></div>

          <!-- 欢迎信息 -->
          <div id="welcomeMessage" class="welcome-message">
            <div class="text-center">
              <i class="fas fa-file-text fa-5x text-muted mb-4"></i>
              <h3>欢迎使用 BM25 中文文档检索系统</h3>
              <p class="lead">请先上传文档，然后进行搜索</p>
              <div class="features">
                <div class="row">
                  <div class="col-md-4">
                    <i class="fas fa-language fa-2x text-primary"></i>
                    <h5>中文分词</h5>
                    <p>使用 jieba 进行精确的中文分词</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-bolt fa-2x text-warning"></i>
                    <h5>BM25 算法</h5>
                    <p>基于 BM25 的高效文档检索</p>
                  </div>
                  <div class="col-md-4">
                    <i class="fas fa-database fa-2x text-success"></i>
                    <h5>向量存储</h5>
                    <p>使用 Qdrant 稀疏向量存储</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/app.js"></script>
  </body>
</html>
```

### 4.2 CSS 样式 (static/css/style.css)

```css
body {
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.sidebar {
  background-color: #343a40;
  color: white;
  min-height: 100vh;
  padding: 20px;
}

.sidebar-header {
  border-bottom: 1px solid #495057;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.sidebar-header h4 {
  margin: 0;
  color: #fff;
}

.upload-section,
.search-section {
  background-color: #495057;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.upload-section h5,
.search-section h5 {
  color: #fff;
  margin-bottom: 15px;
  border-bottom: 1px solid #6c757d;
  padding-bottom: 8px;
}

.main-content {
  padding: 20px;
}

.content-header {
  border-bottom: 2px solid #dee2e6;
  padding-bottom: 15px;
  margin-bottom: 20px;
}

.search-result-item {
  background-color: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.search-result-item:hover {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.result-score {
  background-color: #007bff;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.8em;
  font-weight: bold;
}

.result-source {
  color: #6c757d;
  font-size: 0.9em;
  margin-bottom: 8px;
}

.result-text {
  line-height: 1.6;
  color: #333;
}

.welcome-message {
  text-align: center;
  padding: 60px 20px;
}

.features {
  margin-top: 40px;
}

.features .col-md-4 {
  padding: 20px;
}

.features h5 {
  margin: 15px 0 10px 0;
  color: #333;
}

.features p {
  color: #6c757d;
  font-size: 0.9em;
}

.alert-custom {
  border-radius: 8px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

#uploadStatus .alert {
  margin-top: 10px;
  margin-bottom: 0;
}

.btn {
  border-radius: 6px;
  font-weight: 500;
}

.form-control,
.form-select {
  border-radius: 6px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    min-height: auto;
    margin-bottom: 20px;
  }

  .main-content {
    padding: 15px;
  }

  .welcome-message {
    padding: 30px 15px;
  }

  .features .col-md-4 {
    margin-bottom: 20px;
  }
}

/* 加载动画 */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

/* 搜索结果高亮 */
.highlight {
  background-color: #fff3cd;
  padding: 2px 4px;
  border-radius: 3px;
}
```
